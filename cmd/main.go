package main

import (
	"context"
	"flag"
	"syscall"
	"time"

	"github.com/containerd/nri/pkg/api"
	"github.com/containerd/nri/pkg/stub"
	"k8s.io/component-base/logs"
	"k8s.io/klog/v2"
)

var (
	verbose        bool
	sourceHostPath string
	mountOption    = []string{"rbind", "ro", "rprivate"}
)

// an annotated mount
type mount struct {
	Source      string   `json:"source"`
	Destination string   `json:"destination"`
	Type        string   `json:"type"`
	Options     []string `json:"options"`
}

// our injector plugin
type plugin struct {
	stub stub.Stub
}

// CreateContainer handles container creation requests.
func (p *plugin) CreateContainer(_ context.Context, pod *api.PodSandbox, ctr *api.Container) (*api.ContainerAdjustment, []*api.ContainerUpdate, error) {
	if verbose {
		dump("CreateContainer", "pod", pod, "container", ctr)
	}

	adjust := &api.ContainerAdjustment{}

	if IsVGPUContainer(ctr) {
		if err := injectMounts([]mount{AddHookSo()}, pod, ctr, adjust); err != nil {
			klog.ErrorS(err, "failed to inject libhook.so")
			return nil, nil, err
		}
		if err := injectMounts([]mount{AddVGPULdPreload()}, pod, ctr, adjust); err != nil {
			klog.ErrorS(err, "failed to inject ld.so.preload")
			return nil, nil, err
		}
		if err := injectMounts([]mount{AddLSPCICDB()}, pod, ctr, adjust); err != nil {
			klog.ErrorS(err, "failed to inject lspci")
			return nil, nil, err
		}
		if err := injectMounts([]mount{AddConfigFile()}, pod, ctr, adjust); err != nil {
			klog.ErrorS(err, "failed to inject config.json")
			return nil, nil, err
		}
		klog.Infof("Injected vgpu mounts for %s", containerName(pod, ctr))
	}

	if IsGPUContainer(ctr) {
		if err := injectMounts([]mount{AddHookSo()}, pod, ctr, adjust); err != nil {
			klog.ErrorS(err, "failed to inject libhook.so")
			return nil, nil, err
		}
		if err := injectMounts([]mount{AddGPULdPreload()}, pod, ctr, adjust); err != nil {
			klog.ErrorS(err, "failed to inject ld.so.preload")
			return nil, nil, err
		}
		if err := injectMounts([]mount{AddLSPCICDB()}, pod, ctr, adjust); err != nil {
			klog.Errorf("failed to inject lspci: %v", err)
			return nil, nil, err
		}
		if err := injectMounts([]mount{AddConfigFile()}, pod, ctr, adjust); err != nil {
			klog.ErrorS(err, "failed to inject config.json")
			return nil, nil, err
		}
		klog.Infof("Injected gpu mounts for %s", containerName(pod, ctr))
	}

	if verbose {
		dump(containerName(pod, ctr), "ContainerAdjustment", adjust)
	}

	return adjust, nil, nil
}

func injectMounts(mounts []mount, pod *api.PodSandbox, ctr *api.Container, a *api.ContainerAdjustment) error {
	if len(mounts) == 0 {
		klog.V(5).Infof("%s: no mounts annotated...", containerName(pod, ctr))
		return nil
	}

	if verbose {
		dump(containerName(pod, ctr), "annotated mounts", mounts)
	}

	for _, m := range mounts {
		a.AddMount(m.toNRI())
		if !verbose {
			klog.Infof("%s: injected mount %q -> %q...", containerName(pod, ctr),
				m.Source, m.Destination)
		}
	}

	return nil
}

// Convert a device to the NRI API representation.
func (m *mount) toNRI() *api.Mount {
	apiMnt := &api.Mount{
		Source:      m.Source,
		Destination: m.Destination,
		Type:        m.Type,
		Options:     m.Options,
	}
	return apiMnt
}

// Construct a container name for klog messages.
func containerName(pod *api.PodSandbox, container *api.Container) string {
	if pod != nil {
		return pod.Name + "/" + container.Name
	}
	return container.Name
}

var (
	pluginName string
	pluginIdx  string
	// autoEnableNRIPlugin is a flag to enable the NRI plugin automatically by containerd.
	// if set to true, we will change /etc/containerd/config.toml nri config disable to false
	autoEnableNRIPlugin = true
)

func main() {
	logs.InitLogs()
	klog.InitFlags(nil)
	klog.EnableContextualLogging(true)
	defer logs.FlushLogs()

	flag.StringVar(&pluginName, "name", "maskgpu-plugin", "plugin name to register to NRI")
	flag.StringVar(&pluginIdx, "idx", "01", "plugin index to register to NRI")
	flag.BoolVar(&verbose, "verbose", false, "enable (more) verbose klogging")
	flag.StringVar(&sourceHostPath, "source-path", "/usr/local/maskgpu", "source host path for mounts")
	flag.IntVar(&ProbePort, "probe-port", 8888, "probe port")
	flag.BoolVar(&autoEnableNRIPlugin, "auto-enable-nri-plugin", true, "auto enable nri plugin")
	flag.Parse()

	var (
		err  error
		opts []stub.Option
	)

	if pluginName != "" {
		opts = append(opts, stub.WithPluginName(pluginName))
	}
	if pluginIdx != "" {
		opts = append(opts, stub.WithPluginIdx(pluginIdx))
	}

	go func() {
		klog.Info("Init Probe http server")
		if err := InitProbe(); err != nil {
			klog.Fatalf("failed init probe: %v", err)
		}
	}()

	if err = start(opts); err != nil {
		klog.Fatalf("failed to start plugin: %v", err)
	}
}

func start(opts []stub.Option) error {
	var err error
	/*Loading config files*/
	klog.Info("Starting OS watcher.")
	sigs := newOSWatcher(syscall.SIGHUP, syscall.SIGINT, syscall.SIGTERM, syscall.SIGQUIT)

	var p *plugin
	p = &plugin{}
	closeCtx := make(chan struct{})

restart:
	ctx := context.Background()
	opts = append(opts, stub.WithOnClose(func() {
		klog.Info("Stopping plugins.")
		closeCtx <- struct{}{}
	}))
	if p.stub, err = stub.New(p, opts...); err != nil {
		klog.Fatalf("failed to create plugin stub: %v", err)
	}
	go func() {
		err = p.stub.Run(ctx)
		if err != nil {
			klog.Errorf("plugin exited with error %v", err)
			closeCtx <- struct{}{}
		}
	}()

	for {
		select {
		case <-closeCtx:
			klog.Infof("Restarting plugin.")
			time.Sleep(3 * time.Second)
			goto restart

		case <-ctx.Done():
			goto restart

		case s := <-sigs:
			switch s {
			case syscall.SIGHUP:
				klog.Info("Received SIGHUP, restarting.")
				goto restart
			default:
				klog.Infof("Received signal \"%v\", shutting down.", s)
				goto exit
			}
		}
	}
exit:
	stopPlugins(p)
	return nil
}

func stopPlugins(p *plugin) {
	klog.Info("Stopping plugins.")
	p.stub.Stop()
}
