package main

import (
	"fmt"
	"strings"

	"github.com/containerd/nri/pkg/api"
)

const (
	VGPUDestinationPath = "/usr/local/vgpu/libvgpu.so"
)

const (
	DeviceNvidiaVisibleDevicesEnv  = "NVIDIA_VISIBLE_DEVICES"
	DeviceNvidiaVisibleDevicesVoid = DeviceNvidiaVisibleDevicesEnv + "=" + "void"
)

const (
	MaskGPUSoName        = "libhook.so"
	VGPULdPreloadName    = "vgpu-ld.so.preload"
	GPULdPreloadName     = "gpu-ld.so.preload"
	ContainerMaskGPUPath = "/usr/local/maskgpu"
	PCIIdsName           = "pci.ids"
	ContainerPCIIdsPath  = "/usr/share/misc"
	ConfigFileName       = "config.json"
	ContainerConfigPath  = "/etc/maskgpu"
)

func IsVGPUContainer(ctr *api.Container) bool {
	if ctr.GetMounts() == nil || len(ctr.GetMounts()) == 0 {
		return false
	}
	for _, mounts := range ctr.GetMounts() {
		if mounts.GetDestination() == VGPUDestinationPath {
			return true
		}
	}
	return false
}

func IsGPUContainer(ctr *api.Container) bool {
	if IsVGPUContainer(ctr) {
		return false
	}
	if ctr.GetEnv() == nil || len(ctr.GetEnv()) == 0 {
		return false
	}
	for _, env := range ctr.GetEnv() {
		if strings.HasPrefix(env, DeviceNvidiaVisibleDevicesEnv) {
			if env == DeviceNvidiaVisibleDevicesVoid {
				return false
			}
			return true
		}
	}
	return false
}

func AddHookSo() mount {
	return mount{
		Source:      fmt.Sprintf("%s/%s", sourceHostPath, MaskGPUSoName),
		Destination: fmt.Sprintf("%s/%s", ContainerMaskGPUPath, MaskGPUSoName),
		Type:        "bind",
		Options:     mountOption,
	}
}

func AddVGPULdPreload() mount {
	return mount{
		Source:      fmt.Sprintf("%s/%s", sourceHostPath, VGPULdPreloadName),
		Destination: "/etc/ld.so.preload",
		Type:        "bind",
		Options:     mountOption,
	}
}

func AddGPULdPreload() mount {
	return mount{
		Source:      fmt.Sprintf("%s/%s", sourceHostPath, GPULdPreloadName),
		Destination: "/etc/ld.so.preload",
		Type:        "bind",
		Options:     mountOption,
	}
}

func AddLSPCICDB() mount {
	return mount{
		Source:      fmt.Sprintf("%s/%s", sourceHostPath, PCIIdsName),
		Destination: fmt.Sprintf("%s/%s", ContainerPCIIdsPath, PCIIdsName),
		Type:        "bind",
		Options:     mountOption,
	}
}

func AddConfigFile() mount {
	return mount{
		Source:      fmt.Sprintf("%s/%s", sourceHostPath, ConfigFileName),
		Destination: fmt.Sprintf("%s/%s", ContainerConfigPath, ConfigFileName),
		Type:        "bind",
		Options:     mountOption,
	}
}
