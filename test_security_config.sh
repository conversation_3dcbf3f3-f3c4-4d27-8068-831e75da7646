#!/bin/bash

# GPU配置安全方案测试脚本

set -e

echo "=== GPU配置安全方案测试 ==="

# 创建测试配置
cat > test_config.json << EOF
{
  "Tesla T4": "道客 X4",
  "Tesla P4": "道客 Y4", 
  "Tesla V100": "道客 Z8",
  "英伟达 RTX 3080": "道客游戏专业版"
}
EOF

echo "✓ 创建测试配置文件: test_config.json"

# 测试方案1：加密配置
echo
echo "=== 测试方案1：加密配置 ==="

# 编译加密工具
echo "编译加密工具..."
cd tools
make encrypt-tool
cd ..

echo "✓ 加密工具编译完成"

# 加密配置文件
echo "加密配置文件..."
./tools/encrypt_config encrypt test_config.json test_config.json.enc

echo "✓ 配置文件加密完成"

# 验证加密文件不可读
echo "验证加密文件内容:"
echo "原始文件:"
head -3 test_config.json
echo "加密文件 (二进制，不可读):"
file test_config.json.enc

# 解密验证
echo "解密验证..."
./tools/encrypt_config decrypt test_config.json.enc test_config_decrypted.json

echo "解密后内容:"
head -3 test_config_decrypted.json

# 比较原始文件和解密文件
if diff test_config.json test_config_decrypted.json > /dev/null; then
    echo "✓ 加密/解密验证成功"
else
    echo "✗ 加密/解密验证失败"
    exit 1
fi

# 测试方案2：嵌入式配置
echo
echo "=== 测试方案2：嵌入式配置 ==="

# 生成嵌入式配置
echo "生成嵌入式配置..."
cd tools
python3 generate_embedded_config.py ../test_config.json ../src/embedded_config.cpp
cd ..

echo "✓ 嵌入式配置生成完成"

# 显示生成的C++代码片段
echo "生成的C++代码片段:"
head -20 src/embedded_config.cpp

# 编译项目
echo "编译项目..."
make build

echo "✓ 项目编译完成"

# 验证嵌入式配置
echo "验证嵌入式配置..."
if strings build/libhook.so | grep -q "道客 X4"; then
    echo "✓ 嵌入式配置验证成功 - 配置已嵌入到SO文件中"
else
    echo "✗ 嵌入式配置验证失败"
    exit 1
fi

# 清理测试文件
echo
echo "=== 清理测试文件 ==="
rm -f test_config.json test_config.json.enc test_config_decrypted.json

echo "✓ 测试完成！"

echo
echo "=== 总结 ==="
echo "✓ 方案1 (加密配置): 配置文件被加密，无法直接读取"
echo "✓ 方案2 (嵌入式配置): 配置编译到SO文件中，无法提取"
echo
echo "两种方案都可以有效保护GPU映射配置不被用户直接读取和解析。"
echo "生产环境推荐使用嵌入式配置方案。"
