/*
 * HCS API
 *
 * No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)
 *
 * API version: 2.1
 * Generated by: Swagger Codegen (https://github.com/swagger-api/swagger-codegen.git)
 */

package hcsschema

type Uefi struct {
	EnableDebugger bool `json:"EnableDebugger,omitempty"`

	ApplySecureBootTemplate string `json:"ApplySecureBootTemplate,omitempty"`

	SecureBootTemplateId string `json:"SecureBootTemplateId,omitempty"`

	BootThis *UefiBootEntry `json:"BootThis,omitempty"`

	Console string `json:"Console,omitempty"`
}
