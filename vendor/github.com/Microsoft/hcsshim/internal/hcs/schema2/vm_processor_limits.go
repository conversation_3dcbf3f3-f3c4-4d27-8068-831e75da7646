/*
 * HCS API
 *
 * No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)
 *
 * API version: 2.4
 * Generated by: Swagger Codegen (https://github.com/swagger-api/swagger-codegen.git)
 */

package hcsschema

// ProcessorLimits is used when modifying processor scheduling limits of a virtual machine.
type ProcessorLimits struct {
	// Maximum amount of host CPU resources that the virtual machine can use.
	Limit uint64 `json:"Limit,omitempty"`
	// Value describing the relative priority of this virtual machine compared to other virtual machines.
	Weight uint64 `json:"Weight,omitempty"`
	// Minimum amount of host CPU resources that the virtual machine is guaranteed.
	Reservation uint64 `json:"Reservation,omitempty"`
	// Provides the target maximum CPU frequency, in MHz, for a virtual machine.
	MaximumFrequencyMHz uint32 `json:"MaximumFrequencyMHz,omitempty"`
}
