cmake_minimum_required(VERSION 3.10)

# 项目名称
project(maskgpu)

# 设置C++标准
set(CMAKE_CXX_STANDARD 11)
set(CMAKE_CXX_STANDARD_REQUIRED ON)

# 添加头文件目录
include_directories(src)

# 添加 src 目录下的所有源文件
file(GLOB SOURCES "src/*.cpp")

# 确保包含嵌入式配置文件（如果存在）
if(EXISTS "${CMAKE_SOURCE_DIR}/src/embedded_config.cpp")
    message(STATUS "Found embedded config, including in build")
else()
    message(STATUS "No embedded config found, using default")
endif()

# 添加动态库
add_library(hook SHARED ${SOURCES})

# 链接 dl 库
target_link_libraries(hook dl)