# Default values for maskgpu.
# This is a YAML-formatted file.
# Declare variables to be passed into your templates.

# This sets the container image more information can be found here: https://kubernetes.io/docs/concepts/containers/images/
image:
  registry: release-ci.daocloud.io
  repository: zestu/maskgpu
  # This sets the pull policy for images.
  pullPolicy: IfNotPresent
  # Overrides the image tag whose default is the chart appVersion.
  tag: "latest"

# This is for the secretes for pulling an image from a private repository more information can be found here: https://kubernetes.io/docs/tasks/configure-pod-container/pull-image-private-registry/
imagePullSecrets: [ ]
# This is to override the chart name.
nameOverride: ""
fullnameOverride: ""


# This is for setting Kubernetes Annotations to a Pod.
# For more information checkout: https://kubernetes.io/docs/concepts/overview/working-with-objects/annotations/ 
podAnnotations: { }
# This is for setting Kubernetes Labels to a Pod.
# For more information checkout: https://kubernetes.io/docs/concepts/overview/working-with-objects/labels/
podLabels: { }

podSecurityContext: { }
# fsGroup: 2000

securityContext: { }
  # capabilities:
  #   drop:
  #   - ALL
  # readOnlyRootFilesystem: true
  # runAsNonRoot: true
# runAsUser: 1000

resources: { }
  # We usually recommend not to specify default resources and to leave this as a conscious
  # choice for the user. This also increases chances charts run on environments with little
  # resources, such as Minikube. If you do want to specify resources, uncomment the following
  # lines, adjust them as necessary, and remove the curly braces after 'resources:'.
  # limits:
  #   cpu: 100m
  #   memory: 128Mi
  # requests:
  #   cpu: 100m
#   memory: 128Mi

# Additional volumes on the output Deployment definition.
#volumes: []
volumes:
- name: maskgpu
  hostPath:
    path: /usr/local/maskgpu
    type: DirectoryOrCreate
# - name: foo
#   secret:
#     secretName: mysecret
#     optional: false

# Additional volumeMounts on the output Deployment definition.
#volumeMounts: []
volumeMounts:
- name: "maskgpu"
  mountPath: "/maskgpu_hook"
# - name: foo
#   mountPath: "/etc/foo"
#   readOnly: true

nodeSelector: {}

tolerations: []

affinity: {}

initContainerImage:
  registry: ghcr.io
  repository: containers/nri-plugins/nri-config-manager
  # If not defined Chart.AppVersion will be used
  tag: v0.8.0
  pullPolicy: IfNotPresent

nri:
  plugin:
    index: "90"
    name: maskgpu
    verbose: false
  runtime:
    patchConfig: true
  config:
    pluginRegistrationTimeout: 5s
    pluginRequestTimeout: 2s