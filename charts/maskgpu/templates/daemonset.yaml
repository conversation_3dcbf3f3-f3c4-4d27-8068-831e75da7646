apiVersion: apps/v1
kind: DaemonSet
metadata:
  name: {{ include "maskgpu.fullname" . }}
  labels:
    {{- include "maskgpu.labels" . | nindent 4 }}
spec:
  selector:
    matchLabels:
      {{- include "maskgpu.selectorLabels" . | nindent 6 }}
  template:
    metadata:
      {{- with .Values.podAnnotations }}
      annotations:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      labels:
        {{- include "maskgpu.labels" . | nindent 8 }}
        {{- with .Values.podLabels }}
        {{- toYaml . | nindent 8 }}
        {{- end }}
    spec:
      {{- with .Values.imagePullSecrets }}
      imagePullSecrets:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      securityContext:
        {{- toYaml .Values.podSecurityContext | nindent 8 }}
      nodeSelector:
        kubernetes.io/os: "linux"
      {{- with .Values.nodeSelector }}
          {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- if .Values.nri.runtime.patchConfig }}
      initContainers:
      - name: patch-runtime
        {{- if (not (or (eq .Values.nri.runtime.config nil) (eq .Values.nri.runtime.config.pluginRegistrationTimeout ""))) }}
        args:
          - -nri-plugin-registration-timeout
          - {{ .Values.nri.runtime.config.pluginRegistrationTimeout }}
          - -nri-plugin-request-timeout
          - {{ .Values.nri.runtime.config.pluginRequestTimeout }}
        {{- end }}
        image: {{ .Values.initContainerImage.registry }}/{{ .Values.initContainerImage.repository }}:{{ .Values.initContainerImage.tag | default .Chart.AppVersion }}
        imagePullPolicy: {{ .Values.initContainerImage.pullPolicy }}
        volumeMounts:
        - name: containerd-config
          mountPath: /etc/containerd
        - name: crio-config
          mountPath: /etc/crio/crio.conf.d
        - name: dbus-socket
          mountPath: /var/run/dbus/system_bus_socket
        securityContext:
          privileged: true
      {{- end }}
      containers:
      - name: {{ .Chart.Name }}
        env:
        - name: NODE_NAME
          valueFrom:
            fieldRef:
              apiVersion: v1
              fieldPath: spec.nodeName
        {{- if .Values.nri.plugin.gpuMapping }}
        - name: MASKGPU_CONFIG_PATH
          value: "/etc/maskgpu/config.json"
        {{- end }}
        command:
        - /bin/sh
        - -c
        - /usr/local/bin/maskgpu
          {{- if .Values.nri.plugin.index }} --idx {{ .Values.nri.plugin.index }}{{- end }}
          {{- if .Values.nri.plugin.name }} --name {{ .Values.nri.plugin.name }}{{- end }}
          {{- if and .Values.nri.plugin.verbose (eq .Values.nri.plugin.verbose true) }} --verbose{{- end }}
        lifecycle:
          postStart:
            exec:
              command: [ "/bin/sh","-c", "cp -f /maskgpu/* /maskgpu_hook" ]
        securityContext:
          privileged: true
        image: "{{ .Values.image.registry }}/{{ .Values.image.repository }}:{{ .Values.image.tag | default .Chart.AppVersion }}"
        imagePullPolicy: {{ .Values.image.pullPolicy }}
        resources:
          {{- toYaml .Values.resources | nindent 12 }}
        volumeMounts:
        - name: nrisockets
          mountPath: /var/run/nri
        {{- if .Values.nri.plugin.gpuMapping }}
        - name: maskgpu-config
          mountPath: /etc/maskgpu
          readOnly: true
        {{- end }}
        {{- if .Values.volumeMounts }}
        {{- toYaml .Values.volumeMounts | nindent 8 }}
        {{- end }}
      volumes:
      {{- if .Values.volumes }}
      {{- toYaml .Values.volumes | nindent 6 }}
      {{- end }}
      - name: nrisockets
        hostPath:
          path: /var/run/nri
          type: DirectoryOrCreate
      {{- if .Values.nri.plugin.gpuMapping }}
      - name: maskgpu-config
        configMap:
          name: {{ include "maskgpu.fullname" . }}-config
      {{- end }}
      {{- if .Values.nri.runtime.patchConfig }}
      - name: containerd-config
        hostPath:
          path: /etc/containerd/
          type: DirectoryOrCreate
      - name: crio-config
        hostPath:
          path: /etc/crio/crio.conf.d/
          type: DirectoryOrCreate
      - name: dbus-socket
        hostPath:
          path: /var/run/dbus/system_bus_socket
          type: Socket
      {{- end }}
      {{- with .Values.affinity }}
      affinity:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.tolerations }}
      tolerations:
        {{- toYaml . | nindent 8 }}
      {{- end }}
