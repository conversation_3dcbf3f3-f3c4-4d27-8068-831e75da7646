.DEFAULT_GOAL := build

current_dir := $(dir $(abspath $(firstword $(MAKEFILE_LIST))))

clean:
	@echo "Cleaning..."
	@rm -rf build
	@echo "Done."

build: clean
	@echo "Building..."
	@mkdir -p build
	@cd build && cmake .. && make
	@echo "Done."
	@cd $(current_dir)

nri-build:
	@echo "NRI Plugin Building..."
	@CGO_ENABLED=0 go build -mod vendor -o maskgpu ./cmd

docker-build:
	@echo "Building Docker Image..."
	@docker build -t release-ci.daocloud.io/zestu/maskgpu:v0.2.0 -f docker/Dockerfile .



chart-build:
	@echo "Building Helm Chart..."
	@helm lint --with-subcharts --values ./charts/maskgpu/values.yaml ./charts/maskgpu
	@helm package ./charts/maskgpu
	@echo `yq eval '.version' ./charts/maskgpu/Chart.yaml`
	#helm repo add zestu https://release-ci.daocloud.io/chartrepo/zestu --username=admin --password="
	helm cm-push maskgpu-`yq eval '.version' ./charts/maskgpu/Chart.yaml`.tgz zestu
	rm -rf maskgpu-`yq eval '.version' ./charts/maskgpu/Chart.yaml`.tgz
.PHONY: build