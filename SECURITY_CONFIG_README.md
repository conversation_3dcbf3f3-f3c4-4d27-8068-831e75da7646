# GPU配置安全方案

本文档介绍如何将GPU映射配置加密或嵌入到SO文件中，防止用户直接读取和解析配置内容。

## 方案概述

### 方案1：加密配置文件
- 使用XOR加密算法对JSON配置文件进行加密
- 支持自定义密钥
- 运行时自动解密

### 方案2：嵌入式配置
- 将配置编译到SO文件中
- 配置以C++代码形式存在，无法直接提取
- 构建时生成，运行时直接使用

## 使用方法

### 方案1：加密配置文件

#### 1. 编译加密工具
```bash
cd tools
make encrypt-tool
```

#### 2. 加密配置文件
```bash
# 使用默认密钥
./encrypt_config encrypt ../config.json ../config.json.enc

# 使用自定义密钥
./encrypt_config encrypt ../config.json ../config.json.enc "MySecretKey123"
```

#### 3. 使用加密配置
```bash
# 设置环境变量指向加密文件
export MASKGPU_ENCRYPTED_CONFIG_PATH=/etc/maskgpu/config.json.enc

# 可选：设置自定义密钥
export MASKGPU_ENCRYPTION_KEY="MySecretKey123"
```

#### 4. 在Helm Chart中使用
```yaml
# values.yaml
nri:
  plugin:
    # 不设置gpuMapping，使用加密文件
    encryptedConfig:
      enabled: true
      configData: |
        <base64编码的加密内容>
      key: "MySecretKey123"  # 可选
```

### 方案2：嵌入式配置

#### 1. 生成嵌入式配置
```bash
cd tools
python3 generate_embedded_config.py ../config.json ../src/embedded_config.cpp
```

#### 2. 重新编译项目
```bash
cd ..
make build
```

#### 3. 验证嵌入式配置
编译后的SO文件将包含配置，无需外部配置文件。

## 配置优先级

程序按以下优先级加载配置：

1. **加密配置文件** (如果设置了 `MASKGPU_ENCRYPTED_CONFIG_PATH`)
2. **普通配置文件** (如果设置了 `MASKGPU_CONFIG_PATH`)
3. **嵌入式配置** (如果编译时包含)
4. **默认配置** (硬编码的基本映射)

## 安全特性

### 加密方案安全性
- **XOR加密**: 简单但有效，适合基本保护
- **自定义密钥**: 支持环境变量设置密钥
- **二进制格式**: 加密后的文件为二进制，无法直接查看

### 嵌入式方案安全性
- **编译时嵌入**: 配置成为SO文件的一部分
- **代码混淆**: 配置以C++代码形式存在
- **无外部依赖**: 不需要外部配置文件

## 高级用法

### 1. 批量加密配置
```bash
# 加密多个配置文件
for config in config1.json config2.json config3.json; do
    ./encrypt_config encrypt $config ${config}.enc "UniqueKey_${config}"
done
```

### 2. 动态密钥生成
```bash
# 基于机器信息生成密钥
MACHINE_KEY=$(hostname | md5sum | cut -d' ' -f1)
export MASKGPU_ENCRYPTION_KEY="MaskGPU_${MACHINE_KEY}"
```

### 3. 配置验证
```bash
# 验证加密/解密是否正确
./encrypt_config encrypt config.json config.json.enc
./encrypt_config decrypt config.json.enc config_test.json
diff config.json config_test.json
```

## 故障排除

### 常见问题

1. **加密文件无法解密**
   - 检查密钥是否正确
   - 确认文件没有损坏
   - 验证环境变量设置

2. **嵌入式配置不生效**
   - 确认重新编译了项目
   - 检查embedded_config.cpp是否正确生成
   - 验证CMakeLists.txt包含了新文件

3. **配置加载失败**
   - 检查文件路径和权限
   - 查看日志输出确定加载顺序
   - 验证JSON格式是否正确

### 调试命令
```bash
# 查看当前配置状态
export MASKGPU_DEBUG=1

# 测试配置加载
ldd build/libhook.so  # 检查依赖
strings build/libhook.so | grep -i tesla  # 查看嵌入的配置
```

## 性能考虑

- **加密方案**: 运行时解密有轻微性能开销
- **嵌入式方案**: 无运行时开销，但增加SO文件大小
- **内存使用**: 两种方案内存使用相似

## 安全建议

1. **生产环境**: 推荐使用嵌入式配置方案
2. **开发环境**: 可以使用加密配置方案便于调试
3. **密钥管理**: 使用强密钥，避免硬编码
4. **访问控制**: 限制配置文件的访问权限
5. **定期更新**: 定期更换加密密钥
