
#ifndef _GNU_SOURCE
#define _GNU_SOURCE
#endif
#include <dlfcn.h>
#include <stdio.h>
#include <string.h>
#include <map>
#include <string>
#include <unistd.h>
#include "common.h"
#include "config.h"

void replaceNameWithMapValue(char *name, unsigned int length = 256)
{
   // 确保配置已加载
   Config& config = Config::getInstance();
   if (!config.isConfigLoaded()) {
      // 尝试加载默认配置
      std::string configPath = Config::getDefaultConfigPath();

      // 检查是否是加密配置文件
      bool isEncrypted = (configPath.find(".enc") != std::string::npos) ||
                        (getenv("MASKGPU_ENCRYPTED_CONFIG_PATH") != nullptr);

      bool loaded = false;
      if (isEncrypted) {
         loaded = config.loadEncryptedConfig(configPath);
      } else {
         loaded = config.loadConfig(configPath);
      }

      if (!loaded) {
         LOG_WARN("Failed to load config, GPU name mapping disabled");
         return;
      }
   }

   std::string nameStr(name);
   const auto& nameMap = config.getNameMap();
   auto it = nameMap.find(nameStr);
   if (it != nameMap.end())
   {
      const std::string &newValue = it->second;
      size_t new_length = newValue.length();
      if (new_length >= length)
      {
         new_length = length - 1; // 留一个字符给 '\0'
      }
      strncpy(name, newValue.c_str(), new_length);
      name[new_length] = '\0'; // 确保字符串以 '\0' 结尾
      LOG_DEBUG("GPU name mapped: '%s' -> '%s'", nameStr.c_str(), newValue.c_str());
   }
}
// 定义原始dlsym函数指针类型
typedef void *(*original_dlsym_t)(void *, const char *);
// 声明原始dlsym函数指针
static original_dlsym_t real_dlsym = NULL;
static original_dlsym_t intercepted_dlsym = NULL;
void initialize_real_dlsym();
// 原始的 nvmlDeviceGetName 函数指针
static nvmlReturn_t (*original_nvmlDeviceGetName)(nvmlDevice_t device, char *name, unsigned int length) = NULL;
// 拦截的 nvmlDeviceGetName 函数
nvmlReturn_t nvmlDeviceGetName(nvmlDevice_t device, char *name, unsigned int length)
{
   if (original_nvmlDeviceGetName == NULL)
   {
      // 使用 dlopen 加载 nvml 库
      void *handle = dlopen("libnvidia-ml.so.1", RTLD_NOW);
      if (!handle)
      {
         // 输出错误信息
         LOG_ERROR("Failed to open libnvidia-ml.so: %s", dlerror());
         return NVML_ERROR_UNINITIALIZED;
      }
      // 获取原始的 nvmlDeviceGetName 函数指针
      original_nvmlDeviceGetName = (nvmlReturn_t(*)(nvmlDevice_t, char *, unsigned int))real_dlsym(handle, "nvmlDeviceGetName");
      if (original_nvmlDeviceGetName == NULL)
      {
         // 输出错误信息
         LOG_ERROR("Failed to find original nvmlDeviceGetName function: %s", dlerror());
         return NVML_ERROR_UNINITIALIZED;
      }
   }
   if (original_nvmlDeviceGetName)
   {
      nvmlReturn_t res = original_nvmlDeviceGetName(device, name, length);
      if (res == NVML_SUCCESS)
      {
         // 替换设备名称
         replaceNameWithMapValue(name, length);
      }

      return res;
   }
   // 如果找不到原始函数，返回错误
   return NVML_ERROR_UNINITIALIZED;
}
static cudaError_t (*original_cudaGetDeviceProperties)(struct cudaDeviceProp *prop, int device) = NULL;

// 拦截的 cudaGetDeviceProperties 函数
cudaError_t cudaGetDeviceProperties(struct cudaDeviceProp *prop, int device)
{
   // 调用原始的 cudaGetDeviceProperties 函数
   if (original_cudaGetDeviceProperties == NULL)
   {
      original_cudaGetDeviceProperties =
          (cudaError_t(*)(struct cudaDeviceProp *, int))real_dlsym(RTLD_NEXT, "cudaGetDeviceProperties");
   }
   if (original_cudaGetDeviceProperties == NULL)
   {
      // 使用 dlopen 加载 cuda 库
      void *handle = dlopen("libcudart.so", RTLD_LAZY);
      if (!handle)
      {
         // 输出错误信息
         if (original_cudaGetDeviceProperties == NULL)
         {
            original_cudaGetDeviceProperties =
                (cudaError_t(*)(struct cudaDeviceProp *, int))intercepted_dlsym(RTLD_NEXT, "cudaGetDeviceProperties");
         }
         return cudaErrorUnknown;
      }
   }
   if (original_cudaGetDeviceProperties == NULL)
   {
      LOG_ERROR("cudaGetDeviceProperties is NULL");
   }
   if (original_cudaGetDeviceProperties)
   {
      cudaError_t res = original_cudaGetDeviceProperties(prop, device);
      if (res == cudaSuccess)
      {
         // 替换设备名称
         replaceNameWithMapValue(prop->name);
      }
      return res;
   }

   // 如果找不到原始函数，返回错误
   return cudaErrorUnknown;
}

// CUresult cuDeviceGetName(char *name, int len, CUdevice dev)
static CUresult (*original_cuDeviceGetName)(char *name, int len, CUdevice dev) = NULL;

// 拦截的 cuDeviceGetName 函数
CUresult cuDeviceGetName(char *name, int len, CUdevice dev)
{
   // 调用原始的 cudaGetDeviceProperties 函数
   if (original_cuDeviceGetName == NULL)
   {
      original_cuDeviceGetName = (CUresult(*)(char *, int, CUdevice))real_dlsym(RTLD_NEXT, "cuDeviceGetName");
   }
   if (original_cuDeviceGetName == NULL)
   {
      // 使用 dlopen 加载 cuda 库
      void *handle = dlopen("libcuda.so.1", RTLD_LAZY);
      if (!handle)
      {
         // 输出错误信息
         if (original_cuDeviceGetName == NULL)
         {
            original_cuDeviceGetName = (CUresult(*)(char *, int, CUdevice))intercepted_dlsym(RTLD_NEXT, "cuDeviceGetName");
         }
         return CUDA_ERROR_UNKNOWN;
      }
   }
   if (original_cuDeviceGetName == NULL)
   {
      LOG_ERROR("cuDeviceGetName is NULL");
   }
   if (original_cuDeviceGetName)
   {
      CUresult res = original_cuDeviceGetName(name, len, dev);
      if (res == CUDA_SUCCESS)
      {
         // 替换设备名称
         replaceNameWithMapValue(name, len);
      }
      return res;
   }

   // 如果找不到原始函数，返回错误
   return CUDA_ERROR_UNKNOWN;
}
// 拦截 dlsym 函数
void *dlsym(void *handle, const char *symbol)
{
   LOG_DEBUG("dlsym: %s", symbol);
   initialize_real_dlsym();
   // 使用原始的 dlsym 函数指针
   if (strcmp(symbol, "nvmlDeviceGetName") == 0)
   {
      LOG_DEBUG("Hook nvmlDeviceGetName");
      if (!real_dlsym)
      {
         LOG_ERROR("real_dlsym is NULL");
         return NULL;
      }
      // 返回拦截的 nvmlDeviceGetName 函数
      return (void *)nvmlDeviceGetName;
   }
   if (strcmp(symbol, "cudaGetDeviceProperties") == 0)
   {
      if (!real_dlsym)
      {
         LOG_ERROR("real_dlsym is NULL");
         return NULL;
      }
      return (void *)cudaGetDeviceProperties;
   }
   if (strcmp(symbol, "cuDeviceGetName") == 0)
   {
      LOG_DEBUG("Hook cuDeviceGetName");
      if (!real_dlsym)
      {
         LOG_ERROR("real_dlsym is NULL");
         return NULL;
      }
      return (void *)cuDeviceGetName;
   }
   if (intercepted_dlsym == NULL)
   {
      LOG_ERROR("intercepted_dlsym is NULL");
      return NULL;
   }
   if (real_dlsym == NULL)
   {
      LOG_ERROR("real_dlsym is NULL");
      return NULL;
   }
   return intercepted_dlsym(handle, symbol);
}

void initialize_real_dlsym()
{
   if (real_dlsym == NULL)
   {
      LOG_INFO("initialize_real_dlsym");
      // 使用 dlopen 加载 libc 库
      void *handle = dlopen("libc.so.6", RTLD_LAZY);
      if (!handle)
      {
         // 输出错误信息
         LOG_WARN("dlopen failed: %s", dlerror());
         LOG_WARN("Try to open libdl.so.2");
         return;
      }
      real_dlsym = (original_dlsym_t)dlvsym(handle, "dlsym", "GLIBC_2.2.5");
      if (real_dlsym == NULL)
      {
         // 输出错误信息
         // 打开 libdl 库
         handle = dlopen("libdl.so.2", RTLD_LAZY);
         if (!handle)
         {
            LOG_ERROR("dlopen failed: %s", dlerror());
            return;
         }

         // 获取 dlsym 函数地址
         real_dlsym = (original_dlsym_t)dlvsym(handle, "dlsym", "GLIBC_2.2.5");
         if (real_dlsym == NULL)
         {
            LOG_ERROR("Failed to find real dlsym: %s", dlerror());
            dlclose(handle);
            return;
         }
      }
      LOG_DEBUG("real_dlsym %p", real_dlsym);
      intercepted_dlsym = (original_dlsym_t)real_dlsym(RTLD_NEXT, "dlsym");
      LOG_DEBUG("intercepted_dlsym %p", intercepted_dlsym);
   }
}
__attribute__((constructor)) void init()
{
   LOG_INFO("Hack library loaded");
   initialize_real_dlsym();

   // 初始化配置
   Config& config = Config::getInstance();
   std::string configPath = Config::getDefaultConfigPath();

   // 检查是否是加密配置文件
   bool isEncrypted = (configPath.find(".enc") != std::string::npos) ||
                     (getenv("MASKGPU_ENCRYPTED_CONFIG_PATH") != nullptr);

   bool loaded = false;
   if (isEncrypted) {
      loaded = config.loadEncryptedConfig(configPath);
      if (loaded) {
         LOG_INFO("Encrypted GPU name mapping enabled with %zu mappings", config.getNameMap().size());
      }
   } else {
      loaded = config.loadConfig(configPath);
      if (loaded) {
         LOG_INFO("GPU name mapping enabled with %zu mappings", config.getNameMap().size());
      }
   }

   if (!loaded) {
      LOG_WARN("GPU name mapping disabled due to config load failure");
   }
}
