#include "embedded_config.h"

// 这个文件将由构建脚本自动生成
// 包含编译时嵌入的GPU映射配置

const std::map<std::string, std::string>& EmbeddedConfig::getEmbeddedMapping() {
    static const std::map<std::string, std::string> embeddedMapping = {
        // 这些配置将在构建时从JSON文件生成
        {"Tesla T4", "Daocloud X4"},
        {"Tesla P4", "Daocloud Y4"},
        // 更多映射将在构建时添加...
    };
    
    return embeddedMapping;
}

bool EmbeddedConfig::hasEmbeddedConfig() {
    return !getEmbeddedMapping().empty();
}
