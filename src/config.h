#ifndef CONFIG_H
#define CONFIG_H

#include <map>
#include <string>

class Config {
public:
    // 获取单例实例
    static Config& getInstance();
    
    // 加载配置文件
    bool loadConfig(const std::string& configPath);
    
    // 获取GPU名称映射
    const std::map<std::string, std::string>& getNameMap() const;
    
    // 检查是否已加载配置
    bool isConfigLoaded() const;
    
    // 获取默认配置路径
    static std::string getDefaultConfigPath();

private:
    Config() = default;
    ~Config() = default;
    Config(const Config&) = delete;
    Config& operator=(const Config&) = delete;
    
    // 解析JSON字符串
    bool parseJson(const std::string& jsonContent);
    
    // 去除字符串两端的空白字符
    std::string trim(const std::string& str);
    
    // 解析JSON对象中的键值对
    bool parseJsonObject(const std::string& content, std::map<std::string, std::string>& result);
    
    std::map<std::string, std::string> nameMap_;
    bool configLoaded_ = false;
};

#endif // CONFIG_H
