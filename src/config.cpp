#include "config.h"
#include "common.h"
#include "embedded_config.h"
#include <fstream>
#include <sstream>
#include <algorithm>
#include <cctype>
#include <libgen.h>

Config& Config::getInstance() {
    static Config instance;
    return instance;
}

std::string Config::getDefaultConfigPath() {
    // 首先尝试环境变量
    const char* envPath = getenv("MASKGPU_CONFIG_PATH");
    if (envPath && strlen(envPath) > 0) {
        return std::string(envPath);
    }

    // 首先尝试加密配置文件
    const char* encryptedPath = getenv("MASKGPU_ENCRYPTED_CONFIG_PATH");
    if (encryptedPath && strlen(encryptedPath) > 0) {
        return std::string(encryptedPath);
    }

    // 默认路径
    return "/etc/maskgpu/config.json";
}

bool Config::loadConfig(const std::string& configPath) {
    LOG_INFO("Loading config from: %s", configPath.c_str());

    std::ifstream file(configPath);
    if (!file.is_open()) {
        LOG_WARN("Failed to open config file: %s, trying embedded config", configPath.c_str());

        // 尝试使用嵌入式配置
        if (EmbeddedConfig::hasEmbeddedConfig()) {
            nameMap_ = EmbeddedConfig::getEmbeddedMapping();
            configLoaded_ = true;
            LOG_INFO("Using embedded config with %zu mappings", nameMap_.size());
            return true;
        }

        // 最后使用默认映射
        nameMap_ = {
            {"Tesla T4", "Daocloud X4"},
            {"Tesla P4", "Daocloud Y4"}
        };
        configLoaded_ = true;
        LOG_INFO("Using default config mapping");
        return true;
    }
    
    std::stringstream buffer;
    buffer << file.rdbuf();
    std::string content = buffer.str();
    file.close();
    
    if (content.empty()) {
        LOG_ERROR("Config file is empty: %s", configPath.c_str());
        return false;
    }
    
    if (parseJson(content)) {
        configLoaded_ = true;
        LOG_INFO("Config loaded successfully, found %zu mappings", nameMap_.size());
        return true;
    } else {
        LOG_ERROR("Failed to parse config file: %s", configPath.c_str());
        return false;
    }
}

const std::map<std::string, std::string>& Config::getNameMap() const {
    return nameMap_;
}

bool Config::isConfigLoaded() const {
    return configLoaded_;
}

std::string Config::trim(const std::string& str) {
    size_t start = str.find_first_not_of(" \t\n\r");
    if (start == std::string::npos) {
        return "";
    }
    size_t end = str.find_last_not_of(" \t\n\r");
    return str.substr(start, end - start + 1);
}

bool Config::parseJson(const std::string& jsonContent) {
    std::string content = trim(jsonContent);
    
    // 简单的JSON解析，只处理我们需要的格式: {"key": "value", ...}
    if (content.empty() || content[0] != '{' || content[content.length() - 1] != '}') {
        LOG_ERROR("Invalid JSON format: must be an object");
        return false;
    }
    
    // 移除外层的大括号
    content = content.substr(1, content.length() - 2);
    content = trim(content);
    
    return parseJsonObject(content, nameMap_);
}

bool Config::parseJsonObject(const std::string& content, std::map<std::string, std::string>& result) {
    if (content.empty()) {
        return true; // 空对象是有效的
    }
    
    size_t pos = 0;
    while (pos < content.length()) {
        // 跳过空白字符
        while (pos < content.length() && std::isspace(content[pos])) {
            pos++;
        }
        
        if (pos >= content.length()) {
            break;
        }
        
        // 查找键的开始引号
        if (content[pos] != '"') {
            LOG_ERROR("Expected '\"' at position %zu", pos);
            return false;
        }
        pos++; // 跳过开始引号
        
        // 查找键的结束引号
        size_t keyStart = pos;
        while (pos < content.length() && content[pos] != '"') {
            if (content[pos] == '\\' && pos + 1 < content.length()) {
                pos += 2; // 跳过转义字符
            } else {
                pos++;
            }
        }
        
        if (pos >= content.length()) {
            LOG_ERROR("Unterminated string for key");
            return false;
        }
        
        std::string key = content.substr(keyStart, pos - keyStart);
        pos++; // 跳过结束引号
        
        // 跳过空白字符
        while (pos < content.length() && std::isspace(content[pos])) {
            pos++;
        }
        
        // 查找冒号
        if (pos >= content.length() || content[pos] != ':') {
            LOG_ERROR("Expected ':' after key");
            return false;
        }
        pos++; // 跳过冒号
        
        // 跳过空白字符
        while (pos < content.length() && std::isspace(content[pos])) {
            pos++;
        }
        
        // 查找值的开始引号
        if (pos >= content.length() || content[pos] != '"') {
            LOG_ERROR("Expected '\"' for value");
            return false;
        }
        pos++; // 跳过开始引号
        
        // 查找值的结束引号
        size_t valueStart = pos;
        while (pos < content.length() && content[pos] != '"') {
            if (content[pos] == '\\' && pos + 1 < content.length()) {
                pos += 2; // 跳过转义字符
            } else {
                pos++;
            }
        }
        
        if (pos >= content.length()) {
            LOG_ERROR("Unterminated string for value");
            return false;
        }
        
        std::string value = content.substr(valueStart, pos - valueStart);
        pos++; // 跳过结束引号
        
        // 添加到映射中
        result[key] = value;
        LOG_DEBUG("Loaded mapping: '%s' -> '%s'", key.c_str(), value.c_str());
        
        // 跳过空白字符
        while (pos < content.length() && std::isspace(content[pos])) {
            pos++;
        }
        
        // 检查是否有逗号
        if (pos < content.length() && content[pos] == ',') {
            pos++; // 跳过逗号
        } else if (pos < content.length()) {
            LOG_ERROR("Expected ',' or end of object at position %zu", pos);
            return false;
        }
    }
    
    return true;
}

bool Config::loadEncryptedConfig(const std::string& configPath) {
    LOG_INFO("Loading encrypted config from: %s", configPath.c_str());

    std::ifstream file(configPath, std::ios::binary);
    if (!file.is_open()) {
        LOG_WARN("Failed to open encrypted config file: %s, trying regular config", configPath.c_str());
        // 尝试加载普通配置文件
        std::string regularPath = configPath;
        size_t pos = regularPath.find(".enc");
        if (pos != std::string::npos) {
            regularPath = regularPath.substr(0, pos) + ".json";
        }
        return loadConfig(regularPath);
    }

    std::stringstream buffer;
    buffer << file.rdbuf();
    std::string encryptedContent = buffer.str();
    file.close();

    if (encryptedContent.empty()) {
        LOG_ERROR("Encrypted config file is empty: %s", configPath.c_str());
        return false;
    }

    // 解密内容
    std::string key = getEncryptionKey();
    std::string content = decryptData(encryptedContent, key);

    if (parseJson(content)) {
        configLoaded_ = true;
        LOG_INFO("Encrypted config loaded successfully, found %zu mappings", nameMap_.size());
        return true;
    } else {
        LOG_ERROR("Failed to parse encrypted config file: %s", configPath.c_str());
        return false;
    }
}

std::string Config::encryptData(const std::string& data, const std::string& key) {
    std::string result = data;
    size_t keyLen = key.length();

    for (size_t i = 0; i < result.length(); ++i) {
        result[i] ^= key[i % keyLen];
    }

    return result;
}

std::string Config::decryptData(const std::string& data, const std::string& key) {
    // XOR加密是对称的，解密和加密使用相同的函数
    return encryptData(data, key);
}

std::string Config::getEncryptionKey() {
    // 从环境变量获取密钥
    const char* envKey = getenv("MASKGPU_ENCRYPTION_KEY");
    if (envKey && strlen(envKey) > 0) {
        return std::string(envKey);
    }

    // 默认密钥（在生产环境中应该使用更复杂的密钥）
    return "MaskGPU_Default_Key_2024";
}
