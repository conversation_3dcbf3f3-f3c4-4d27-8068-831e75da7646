# GPU名称映射配置

## 概述

MaskGPU现在支持通过JSON配置文件来定义GPU名称映射，而不是硬编码在程序中。这使得用户可以灵活地配置GPU名称的映射关系。

## 配置文件格式

配置文件使用JSON格式，结构如下：

```json
{
  "原始GPU名称": "映射后的GPU名称",
  "Tesla T4": "Daocloud X4",
  "Tesla P4": "Daocloud Y4",
  "Tesla V100": "Daocloud Z8"
}
```

## 配置文件位置

程序会按以下优先级查找配置文件：

1. **环境变量指定的路径**：设置 `MASKGPU_CONFIG_PATH` 环境变量
   ```bash
   export MASKGPU_CONFIG_PATH=/path/to/your/config.json
   ```

2. **默认路径**：`/etc/maskgpu/config.json`

## 使用方法

### 1. 创建配置文件

创建一个JSON文件，定义你需要的GPU名称映射：

```bash
sudo mkdir -p /etc/maskgpu
sudo cp config.json /etc/maskgpu/config.json
```

### 2. 自定义配置路径

如果你想使用自定义路径：

```bash
export MASKGPU_CONFIG_PATH=/home/<USER>/my-gpu-config.json
```

### 3. 验证配置

编译并运行程序，检查日志输出：

```bash
make build
# 查看日志确认配置加载成功
```

## 配置文件示例

项目根目录下的 `config.json` 文件提供了一个完整的示例：

```json
{
  "Tesla T4": "Daocloud X4",
  "Tesla P4": "Daocloud Y4",
  "Tesla V100": "Daocloud Z8",
  "GeForce RTX 3080": "Daocloud Gaming Pro",
  "GeForce RTX 4090": "Daocloud Gaming Ultra"
}
```

## 故障排除

### 配置文件未找到

如果配置文件未找到，程序会：
1. 记录警告日志
2. 使用默认的硬编码映射（Tesla T4 -> Daocloud X4, Tesla P4 -> Daocloud Y4）
3. 继续正常运行

### 配置文件格式错误

如果JSON格式有误，程序会：
1. 记录错误日志
2. 禁用GPU名称映射功能
3. 继续正常运行（但不进行名称替换）

### 调试

启用调试日志来查看详细的配置加载过程：
- 检查日志中的 "Loading config from" 消息
- 查看 "Loaded mapping" 消息确认映射是否正确加载
- 观察 "GPU name mapped" 消息确认运行时映射是否生效

## 注意事项

1. 配置文件必须是有效的JSON格式
2. 键和值都必须是字符串，用双引号包围
3. GPU名称匹配是精确匹配，区分大小写
4. 如果配置文件为空或无效，程序会回退到默认映射
5. 配置文件在程序启动时加载，运行时修改配置文件不会立即生效
