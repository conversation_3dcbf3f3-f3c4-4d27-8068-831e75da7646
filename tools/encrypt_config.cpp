#include <iostream>
#include <fstream>
#include <string>
#include <sstream>

class ConfigEncryptor {
public:
    static std::string encryptData(const std::string& data, const std::string& key) {
        std::string result = data;
        size_t keyLen = key.length();
        
        for (size_t i = 0; i < result.length(); ++i) {
            result[i] ^= key[i % keyLen];
        }
        
        return result;
    }
    
    static std::string decryptData(const std::string& data, const std::string& key) {
        // XOR加密是对称的
        return encryptData(data, key);
    }
};

void printUsage(const char* programName) {
    std::cout << "用法: " << programName << " <操作> <输入文件> <输出文件> [密钥]" << std::endl;
    std::cout << "操作:" << std::endl;
    std::cout << "  encrypt  - 加密JSON配置文件" << std::endl;
    std::cout << "  decrypt  - 解密配置文件" << std::endl;
    std::cout << "示例:" << std::endl;
    std::cout << "  " << programName << " encrypt config.json config.json.enc" << std::endl;
    std::cout << "  " << programName << " decrypt config.json.enc config.json" << std::endl;
    std::cout << "  " << programName << " encrypt config.json config.json.enc MySecretKey" << std::endl;
}

int main(int argc, char* argv[]) {
    if (argc < 4) {
        printUsage(argv[0]);
        return 1;
    }
    
    std::string operation = argv[1];
    std::string inputFile = argv[2];
    std::string outputFile = argv[3];
    std::string key = (argc > 4) ? argv[4] : "MaskGPU_Default_Key_2024";
    
    if (operation != "encrypt" && operation != "decrypt") {
        std::cerr << "错误: 无效的操作 '" << operation << "'" << std::endl;
        printUsage(argv[0]);
        return 1;
    }
    
    // 读取输入文件
    std::ifstream inFile(inputFile, std::ios::binary);
    if (!inFile.is_open()) {
        std::cerr << "错误: 无法打开输入文件 '" << inputFile << "'" << std::endl;
        return 1;
    }
    
    std::stringstream buffer;
    buffer << inFile.rdbuf();
    std::string content = buffer.str();
    inFile.close();
    
    if (content.empty()) {
        std::cerr << "错误: 输入文件为空" << std::endl;
        return 1;
    }
    
    // 执行加密或解密
    std::string result;
    if (operation == "encrypt") {
        result = ConfigEncryptor::encryptData(content, key);
        std::cout << "加密完成: " << inputFile << " -> " << outputFile << std::endl;
    } else {
        result = ConfigEncryptor::decryptData(content, key);
        std::cout << "解密完成: " << inputFile << " -> " << outputFile << std::endl;
    }
    
    // 写入输出文件
    std::ofstream outFile(outputFile, std::ios::binary);
    if (!outFile.is_open()) {
        std::cerr << "错误: 无法创建输出文件 '" << outputFile << "'" << std::endl;
        return 1;
    }
    
    outFile.write(result.c_str(), result.length());
    outFile.close();
    
    std::cout << "文件大小: " << result.length() << " 字节" << std::endl;
    std::cout << "使用的密钥: " << (argc > 4 ? "自定义密钥" : "默认密钥") << std::endl;
    
    if (operation == "decrypt") {
        std::cout << "\n解密后的内容预览:" << std::endl;
        std::cout << result.substr(0, std::min(size_t(200), result.length())) << std::endl;
        if (result.length() > 200) {
            std::cout << "..." << std::endl;
        }
    }
    
    return 0;
}
