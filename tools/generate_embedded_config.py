#!/usr/bin/env python3
"""
生成嵌入式配置文件的工具
将JSON配置文件转换为C++源代码，编译到SO文件中
"""

import json
import sys
import os
from pathlib import Path

def escape_string(s):
    """转义C++字符串中的特殊字符"""
    return s.replace('\\', '\\\\').replace('"', '\\"').replace('\n', '\\n').replace('\r', '\\r')

def generate_embedded_config(json_file, output_file):
    """从JSON文件生成C++嵌入配置源文件"""
    
    try:
        with open(json_file, 'r', encoding='utf-8') as f:
            config_data = json.load(f)
    except Exception as e:
        print(f"错误: 无法读取JSON文件 {json_file}: {e}")
        return False
    
    if not isinstance(config_data, dict):
        print("错误: JSON文件必须包含一个对象")
        return False
    
    # 生成C++代码
    cpp_content = '''#include "embedded_config.h"

// 自动生成的嵌入式配置文件
// 请勿手动编辑此文件

const std::map<std::string, std::string>& EmbeddedConfig::getEmbeddedMapping() {
    static const std::map<std::string, std::string> embeddedMapping = {
'''
    
    # 添加映射条目
    for key, value in config_data.items():
        escaped_key = escape_string(str(key))
        escaped_value = escape_string(str(value))
        cpp_content += f'        {{"{escaped_key}", "{escaped_value}"}},\n'
    
    cpp_content += '''    };
    
    return embeddedMapping;
}

bool EmbeddedConfig::hasEmbeddedConfig() {
    return !getEmbeddedMapping().empty();
}
'''
    
    try:
        with open(output_file, 'w', encoding='utf-8') as f:
            f.write(cpp_content)
        print(f"成功生成嵌入式配置文件: {output_file}")
        print(f"包含 {len(config_data)} 个GPU映射")
        return True
    except Exception as e:
        print(f"错误: 无法写入输出文件 {output_file}: {e}")
        return False

def main():
    if len(sys.argv) != 3:
        print("用法: python3 generate_embedded_config.py <input.json> <output.cpp>")
        print("示例: python3 generate_embedded_config.py config.json src/embedded_config.cpp")
        sys.exit(1)
    
    input_file = sys.argv[1]
    output_file = sys.argv[2]
    
    if not os.path.exists(input_file):
        print(f"错误: 输入文件不存在: {input_file}")
        sys.exit(1)
    
    # 确保输出目录存在
    output_dir = os.path.dirname(output_file)
    if output_dir and not os.path.exists(output_dir):
        os.makedirs(output_dir)
    
    if generate_embedded_config(input_file, output_file):
        print("嵌入式配置生成完成！")
        print("现在可以重新编译项目以包含新的配置。")
    else:
        sys.exit(1)

if __name__ == "__main__":
    main()
