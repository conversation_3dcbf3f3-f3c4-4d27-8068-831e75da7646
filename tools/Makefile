# 配置加密和嵌入工具的Makefile

CXX = g++
CXXFLAGS = -std=c++11 -O2

# 目标
.PHONY: all clean encrypt-tool embed-config help

all: encrypt-tool

# 编译加密工具
encrypt-tool: encrypt_config
	@echo "加密工具编译完成: ./encrypt_config"

encrypt_config: encrypt_config.cpp
	$(CXX) $(CXXFLAGS) -o $@ $<

# 生成嵌入式配置
embed-config: ../config.json
	@echo "生成嵌入式配置..."
	python3 generate_embedded_config.py ../config.json ../src/embedded_config.cpp
	@echo "嵌入式配置已生成，请重新编译主项目"

# 加密配置文件
encrypt: encrypt_config ../config.json
	@echo "加密配置文件..."
	./encrypt_config encrypt ../config.json ../config.json.enc
	@echo "加密完成: config.json.enc"

# 解密配置文件（用于验证）
decrypt: encrypt_config ../config.json.enc
	@echo "解密配置文件..."
	./encrypt_config decrypt ../config.json.enc ../config_decrypted.json
	@echo "解密完成: config_decrypted.json"

# 清理
clean:
	rm -f encrypt_config ../config.json.enc ../config_decrypted.json

# 帮助信息
help:
	@echo "可用的目标:"
	@echo "  all          - 编译所有工具"
	@echo "  encrypt-tool - 编译加密工具"
	@echo "  embed-config - 生成嵌入式配置"
	@echo "  encrypt      - 加密配置文件"
	@echo "  decrypt      - 解密配置文件"
	@echo "  clean        - 清理生成的文件"
	@echo "  help         - 显示此帮助信息"
