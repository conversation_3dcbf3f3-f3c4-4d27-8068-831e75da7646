package main

import (
	"context"
	"fmt"
	"testing"

	"github.com/containerd/nri/pkg/api"
)

// 测试config.json挂载功能
func TestConfigMount() {
	// 模拟一个GPU容器
	ctr := &api.Container{
		Name: "test-gpu-container",
		Env: []*api.KeyValue{
			{Key: "NVIDIA_VISIBLE_DEVICES", Value: "0"},
		},
	}

	// 模拟一个Pod
	pod := &api.PodSandbox{
		Name: "test-pod",
	}

	// 创建plugin实例
	p := &plugin{}

	// 调用CreateContainer方法
	adjust, updates, err := p.Create<PERSON>ontainer(context.Background(), pod, ctr)
	if err != nil {
		fmt.Printf("Error: %v\n", err)
		return
	}

	fmt.Printf("Container Adjustment:\n")
	fmt.Printf("Updates: %v\n", updates)

	// 检查挂载点
	if adjust != nil && adjust.Mounts != nil {
		fmt.Printf("Mounts (%d):\n", len(adjust.Mounts))
		for i, mount := range adjust.Mounts {
			fmt.Printf("  Mount %d:\n", i+1)
			fmt.Printf("    Source: %s\n", mount.Source)
			fmt.Printf("    Destination: %s\n", mount.Destination)
			fmt.Printf("    Type: %s\n", mount.Type)
			fmt.Printf("    Options: %v\n", mount.Options)
			
			// 检查是否包含config.json挂载
			if mount.Destination == "/etc/maskgpu/config.json" {
				fmt.Printf("    ✓ Config.json mount found!\n")
				if mount.Source == "/usr/local/maskgpu/config.json" {
					fmt.Printf("    ✓ Source path is correct!\n")
				} else {
					fmt.Printf("    ✗ Source path is incorrect: expected '/usr/local/maskgpu/config.json', got '%s'\n", mount.Source)
				}
			}
		}
	}
}

func main() {
	// 设置默认的sourceHostPath
	sourceHostPath = "/usr/local/maskgpu"
	
	fmt.Println("=== 测试GPU容器config.json挂载功能 ===")
	TestConfigMount()
	
	fmt.Println("\n=== 测试VGPU容器config.json挂载功能 ===")
	
	// 测试VGPU容器
	vgpuCtr := &api.Container{
		Name: "test-vgpu-container",
		Mounts: []*api.Mount{
			{Destination: "/usr/local/vgpu/libvgpu.so"},
		},
	}

	pod := &api.PodSandbox{
		Name: "test-vgpu-pod",
	}

	p := &plugin{}
	adjust, updates, err := p.CreateContainer(context.Background(), pod, vgpuCtr)
	if err != nil {
		fmt.Printf("Error: %v\n", err)
		return
	}

	fmt.Printf("VGPU Container Adjustment:\n")
	fmt.Printf("Updates: %v\n", updates)

	if adjust != nil && adjust.Mounts != nil {
		fmt.Printf("VGPU Mounts (%d):\n", len(adjust.Mounts))
		configMountFound := false
		for i, mount := range adjust.Mounts {
			fmt.Printf("  Mount %d:\n", i+1)
			fmt.Printf("    Source: %s\n", mount.Source)
			fmt.Printf("    Destination: %s\n", mount.Destination)
			fmt.Printf("    Type: %s\n", mount.Type)
			
			if mount.Destination == "/etc/maskgpu/config.json" {
				configMountFound = true
				fmt.Printf("    ✓ VGPU Config.json mount found!\n")
			}
		}
		
		if !configMountFound {
			fmt.Printf("    ✗ Config.json mount not found in VGPU container!\n")
		}
	}
}
