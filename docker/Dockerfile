FROM --platform=$BUILDPLATFORM nvidia/cuda:12.2.0-devel-ubuntu20.04 AS so-build
WORKDIR /maskgpu
COPY . .
ENV DEBIAN_FRONTEND=noninteractive
RUN apt-get -y update; apt-get -y install cmake git shc
RUN make build


FROM --platform=$BUILDPLATFORM docker.io/library/golang:1.23.3 AS nri-build
WORKDIR /maskgpu
ENV GO111MODULE=on \
    GOPROXY=https://goproxy.cn,direct
COPY . .
ARG TARGETARCH
RUN CGO_ENABLED=0 GOOS=linux GOARCH=$TARGETARCH go build -mod vendor -o maskgpu ./cmd/


FROM ubuntu:22.04 AS pci-build
WORKDIR /maskgpu
RUN apt-get update && apt-get install -y cmake git pciutils curl wget
RUN update-pciids
RUN sed -i 's/Tesla/DaoCloud/g' /usr/share/misc/pci.ids


FROM busybox:latest
WORKDIR /maskgpu
COPY --from=so-build /maskgpu/build/libhook.so /maskgpu/libhook.so
COPY --from=nri-build /maskgpu/maskgpu /usr/local/bin/maskgpu
COPY --from=pci-build /usr/share/misc/pci.ids /maskgpu/pci.ids
COPY ./docker/vgpu-ld.so.preload /maskgpu
COPY ./docker/gpu-ld.so.preload /maskgpu
ENTRYPOINT ["/usr/local/bin/maskgpu"]