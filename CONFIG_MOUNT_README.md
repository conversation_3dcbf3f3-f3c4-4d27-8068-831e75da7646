# GPU配置文件自动挂载功能

## 功能概述

MaskGPU现在支持自动将GPU配置文件挂载到容器中，使得GPU名称映射配置可以在容器运行时生效。

## 挂载详情

### 挂载路径
- **宿主机路径**: `/usr/local/maskgpu/config.json`
- **容器内路径**: `/etc/maskgpu/config.json`
- **挂载类型**: `bind`
- **挂载选项**: `["rbind", "ro", "rprivate"]` (只读挂载)

### 触发条件

配置文件挂载会在以下情况下自动触发：

1. **GPU容器**: 容器环境变量中包含 `NVIDIA_VISIBLE_DEVICES` 且不为 `void`
2. **VGPU容器**: 容器挂载点中包含 `/usr/local/vgpu/libvgpu.so`

## 实现细节

### 代码修改

#### 1. 新增常量定义 (`cmd/healper.go`)
```go
const (
    ConfigFileName       = "config.json"
    ContainerConfigPath  = "/etc/maskgpu"
)
```

#### 2. 新增挂载函数 (`cmd/healper.go`)
```go
func AddConfigFile() mount {
    return mount{
        Source:      fmt.Sprintf("%s/%s", sourceHostPath, ConfigFileName),
        Destination: fmt.Sprintf("%s/%s", ContainerConfigPath, ConfigFileName),
        Type:        "bind",
        Options:     mountOption,
    }
}
```

#### 3. 修改CreateContainer方法 (`cmd/main.go`)
在VGPU和GPU容器的挂载逻辑中都添加了config.json的挂载：

```go
if err := injectMounts([]mount{AddConfigFile()}, pod, ctr, adjust); err != nil {
    klog.ErrorS(err, "failed to inject config.json")
    return nil, nil, err
}
```

## 使用方法

### 1. 准备配置文件

在宿主机上创建GPU映射配置文件：

```bash
sudo mkdir -p /usr/local/maskgpu
sudo cat > /usr/local/maskgpu/config.json << EOF
{
  "Tesla T4": "Daocloud X4",
  "Tesla P4": "Daocloud Y4",
  "NVIDIA H200": "高性能200"
}
EOF
```

### 2. 部署MaskGPU

使用Helm部署MaskGPU：

```bash
helm install maskgpu charts/maskgpu
```

### 3. 运行GPU容器

当运行包含GPU的容器时，配置文件会自动挂载：

```bash
# GPU容器示例
kubectl run gpu-test --image=nvidia/cuda:11.0-base --env="NVIDIA_VISIBLE_DEVICES=0"

# VGPU容器示例（需要相应的VGPU配置）
kubectl run vgpu-test --image=my-vgpu-image
```

### 4. 验证挂载

在容器内验证配置文件是否正确挂载：

```bash
# 进入容器
kubectl exec -it gpu-test -- bash

# 检查配置文件
cat /etc/maskgpu/config.json

# 检查环境变量（如果设置了）
echo $MASKGPU_CONFIG_PATH
```

## 配置优先级

容器内的配置加载优先级：

1. **环境变量指定路径** (`MASKGPU_CONFIG_PATH`)
2. **自动挂载的配置文件** (`/etc/maskgpu/config.json`)
3. **默认硬编码配置**

## 故障排除

### 常见问题

1. **配置文件未挂载**
   - 检查容器是否为GPU/VGPU容器
   - 验证宿主机配置文件是否存在
   - 查看MaskGPU插件日志

2. **配置文件内容错误**
   - 验证JSON格式是否正确
   - 检查文件编码是否为UTF-8
   - 确认文件权限是否正确

3. **挂载权限问题**
   - 确保宿主机文件可读
   - 检查SELinux/AppArmor设置
   - 验证容器运行权限

### 调试命令

```bash
# 查看MaskGPU插件日志
kubectl logs -n kube-system daemonset/maskgpu

# 检查容器挂载点
kubectl exec -it <pod-name> -- mount | grep maskgpu

# 验证配置文件内容
kubectl exec -it <pod-name> -- cat /etc/maskgpu/config.json

# 测试GPU名称映射
kubectl exec -it <pod-name> -- nvidia-smi -L
```

## 安全考虑

1. **只读挂载**: 配置文件以只读方式挂载，防止容器修改
2. **路径限制**: 只挂载指定的配置文件，不暴露整个目录
3. **权限控制**: 遵循最小权限原则

## 性能影响

- **启动时间**: 增加一个挂载点，对容器启动时间影响微乎其微
- **运行时性能**: 无影响，配置在程序启动时加载
- **存储开销**: 配置文件通常很小（< 1KB），存储开销可忽略

## 兼容性

- **Kubernetes版本**: 支持所有支持NRI的Kubernetes版本
- **容器运行时**: 支持containerd和CRI-O
- **操作系统**: 支持Linux系统

## 未来改进

1. **动态配置更新**: 支持运行时配置文件热更新
2. **多配置文件**: 支持按命名空间或标签加载不同配置
3. **配置验证**: 在挂载前验证配置文件格式
4. **配置模板**: 支持配置文件模板和变量替换
